# ULTIMATE BLUEPRINT: Gemini VS Code Assistant

**Version:** 4.0 (The Definitive Guide)
**Status:** Ready for Implementation

---

## **Part 1: Product Vision & Core Principles**

### **1.1. Vision Statement**
To empower developers with an intelligent, context-aware AI assistant seamlessly integrated into VS Code, capable of understanding complex tasks, executing multi-step plans, and collaborating through intuitive, mode-driven interactions. Our goal is to enhance productivity, reduce cognitive load, and accelerate software development cycles.

### **1.2. Core Principles**
*   **Transparency:** The user should always understand what the agent is doing and why.
*   **Control:** The user retains ultimate control, especially over code modifications.
*   **Context-Awareness:** The agent deeply understands the codebase, not just isolated files.
*   **Modularity:** The system is designed for extensibility, allowing easy addition of new tools and features.
*   **Responsiveness:** The UI provides real-time feedback on agent activity.

---

## **Part 2: User Interface & Modes of Operation**

The Gemini Assistant's primary interface is a single, dynamic VS Code Webview Panel. A prominent dropdown or tab system at the top of the panel allows users to switch between distinct operational modes.

### **2.1. General Webview Structure**
*   **Header:** Contains mode selector (dropdown/tabs), settings icon, and possibly a status indicator.
*   **Main Content Area:** Dynamically renders the UI specific to the selected mode.
*   **Input Area:** A multi-line text input for user prompts, with a send button.

### **2.2. Mode: `Chat`**
*   **Purpose:** A read-only, conversational interface for quick questions, code explanations, and general knowledge queries. The agent acts as a knowledgeable expert.
*   **UI/UX:**
    *   **Layout:** A clean, linear chat history. Each message (user or AI) is clearly delineated.
    *   **AI Response Rendering:**
        *   Code blocks: Syntax-highlighted, with a "Copy" button on hover.
        *   File references: Clickable links that open the file in the VS Code editor.
        *   Markdown support: Bold, italics, lists, etc.
    *   **Input:** Standard multi-line text input with a "Send" button.
*   **Workflow:**
    1.  User selects `Chat` mode.
    2.  User types a query (e.g., "Explain the `createOrShow` method in `WebviewPanelProvider.ts`.").
    3.  Agent receives query, uses read-only tools (`readFile`, `readDirectory`, `getCodebaseAST`, `google_web_search`) to gather context.
    4.  Agent generates a natural language response and renders it in the chat history.
*   **Available Tools (Read-Only):** `readFile`, `readDirectory`, `getCodebaseAST`, `findSymbolDefinition`, `google_web_search`.

### **2.3. Mode: `Agent` (Task-Driven & Supervised)**
*   **Purpose:** For executing specific, well-defined tasks that involve modifying the codebase. The agent proposes a plan, and the user approves each significant step (especially code changes).
*   **UI/UX:**
    *   **Layout:**
        *   **Left Panel (Plan View):** Displays the agent's generated plan as an ordered list of steps. Each step has:
            *   A unique ID.
            *   A concise description.
            *   A status indicator (e.g., `⏳ Pending`, `▶️ Executing`, `✅ Done`, `❌ Error`, `⏸️ Awaiting Approval`).
            *   Expandable details for tool calls and their results.
        *   **Right Panel (Conversation & Context):** A chat-like interface for ongoing dialogue, agent thoughts, and crucial interaction points (e.g., diffs, questions).
    *   **Action Buttons:**
        *   "Generate Plan" (after initial prompt).
        *   "Approve & Run Plan" (after plan generation).
        *   "Pause/Resume" (during execution).
        *   "Cancel Task".
        *   "Accept Change" / "Reject Change" (for `showDiffForApproval` tool).
*   **Workflow:**
    1.  User selects `Agent` mode and provides a task (e.g., "Refactor `src/utils/logger.ts` to use `pino` instead of `winston`.").
    2.  The agent generates a detailed, multi-step plan and displays it in the Left Panel.
    3.  User reviews the plan. If satisfied, clicks "Approve & Run Plan".
    4.  Agent executes steps sequentially.
    5.  When a step involves a code modification, the `showDiffForApproval` tool is invoked. The Right Panel displays the diff, and the agent pauses, awaiting user input ("Accept Change" / "Reject Change").
    6.  The UI updates step statuses in real-time.
    7.  Upon completion, the agent provides a summary.
*   **Available Tools:** All tools, with write-actions and critical steps requiring explicit user approval.

### **2.4. Mode: `Auto-Agent` (Autonomous Goal-Seeker)**
*   **Purpose:** For complex, high-level goals where the exact steps are unknown. The agent works autonomously to achieve the goal, logging its thought process and actions in real-time. User intervention is minimal, primarily for stopping the process.
*   **UI/UX:**
    *   **Layout:**
        *   **Main Panel (Thought Stream):** A continuous, scrolling log of the agent's internal monologue, tool calls, and results. Each entry is timestamped and color-coded (e.g., `[THOUGHT]`, `[ACTION]`, `[RESULT]`, `[ERROR]`).
        *   A prominent "Stop Execution" button is always visible.
    *   **Input:** A single text input for the high-level goal.
*   **Workflow:**
    1.  User selects `Auto-Agent` mode and provides a high-level goal (e.g., "Implement user authentication with JWT tokens, including signup, login, and a protected route.").
    2.  Agent immediately begins execution, streaming its thoughts and actions to the UI.
    3.  The agent autonomously plans, executes tools, and self-corrects.
    4.  The user can monitor the progress. If the agent gets stuck or goes off track, the user can click "Stop Execution".
    5.  Upon successful completion or unrecoverable error, the agent provides a final report.
*   **Available Tools:** All tools, with full autonomy. `askUserForInput` can be used for critical ambiguities, but the agent will attempt to resolve issues autonomously first.

---

## **Part 3: The Agent's Complete Toolkit (Technical Specification)**

All tools are exposed to the LLM via Gemini's Function Calling mechanism. Each tool is implemented as a method within the `ToolController` class.

### **3.1. File System Tools**

#### **3.1.1. `readFile`**
*   **Purpose:** Reads the content of a specified file.
*   **Input:** `path: string` (absolute path to the file).
*   **Output:** `Promise<string>` (file content) or `Promise<Error>` if file not found/unreadable.
*   **VS Code API:** `vscode.workspace.fs.readFile(uri)`
*   **Error Handling:** Catches `vscode.FileSystemError.FileNotFound` and returns a specific error message. Other errors (permission, etc.) are also caught and reported.
*   **Example LLM Call:** `{"tool": "readFile", "parameters": {"path": "/src/index.ts"}}`

#### **3.1.2. `readDirectory`**
*   **Purpose:** Lists the names of files and subdirectories within a specified directory.
*   **Input:** `path: string` (absolute path to the directory).
*   **Output:** `Promise<Array<{name: string, type: 'file' | 'directory'}>>` or `Promise<Error>`.
*   **VS Code API:** `vscode.workspace.fs.readDirectory(uri)`
*   **Error Handling:** Catches `vscode.FileSystemError.FileNotFound` and other read errors.
*   **Example LLM Call:** `{"tool": "readDirectory", "parameters": {"path": "/src/components"}}`

#### **3.1.3. `writeFile`**
*   **Purpose:** Writes content to a specified file. Overwrites if the file exists.
*   **Input:** `path: string`, `content: string`.
*   **Output:** `Promise<void>` or `Promise<Error>`.
*   **VS Code API:** `vscode.workspace.fs.writeFile(uri, contentBuffer)`
*   **Error Handling:** Catches permission errors, disk full, etc.
*   **Approval (Agent Mode):** This tool will internally trigger `showDiffForApproval` before actual write.
*   **Example LLM Call:** `{"tool": "writeFile", "parameters": {"path": "/src/new_feature.ts", "content": "export function newFeature() {\n  console.log('Hello');\n}"}}`

#### **3.1.4. `createFile`**
*   **Purpose:** Creates a new, empty file at the specified path. Fails if the file already exists.
*   **Input:** `path: string`.
*   **Output:** `Promise<void>` or `Promise<Error>`.
*   **VS Code API:** `vscode.workspace.fs.writeFile(uri, new Uint8Array())`
*   **Error Handling:** Catches `vscode.FileSystemError.FileExists` and other write errors.
*   **Example LLM Call:** `{"tool": "createFile", "parameters": {"path": "/src/temp/scratchpad.txt"}}`

#### **3.1.5. `moveFile`**
*   **Purpose:** Moves or renames a file or directory.
*   **Input:** `source: string`, `destination: string`.
*   **Output:** `Promise<void>` or `Promise<Error>`.
*   **VS Code API:** `vscode.workspace.fs.rename(sourceUri, destinationUri)`
*   **Error Handling:** Catches `vscode.FileSystemError.FileNotFound`, `vscode.FileSystemError.FileExists`, etc.
*   **Example LLM Call:** `{"tool": "moveFile", "parameters": {"source": "/old/path/file.ts", "destination": "/new/path/file.ts"}}`

### **3.2. Code Intelligence Tools**

#### **3.2.1. `getCodebaseAST`**
*   **Purpose:** Parses specified files (or files matching a glob pattern) into Abstract Syntax Trees (ASTs) to enable structural code analysis.
*   **Input:** `globPattern: string` (e.g., `src/**/*.ts`).
*   **Output:** `Promise<Array<{filePath: string, ast: string}>>` (serialized ASTs) or `Promise<Error>`.
*   **Libraries:** `typescript` (compiler API).
*   **Implementation:** Iterates through files matching the glob, reads content, and uses `ts.createSourceFile` to generate AST. The AST will be stringified (e.g., JSON.stringify) for LLM consumption.
*   **Example LLM Call:** `{"tool": "getCodebaseAST", "parameters": {"globPattern": "src/**/*.ts"}}`

#### **3.2.2. `findSymbolDefinition`**
*   **Purpose:** Locates the definition of a specific symbol (function, class, variable) within the workspace.
*   **Input:** `symbolName: string`.
*   **Output:** `Promise<Array<{filePath: string, line: number, character: number}>>` or `Promise<Error>`.
*   **VS Code API:** `vscode.commands.executeCommand('vscode.executeDefinitionProvider', uri, position)` (requires opening the file temporarily or using language server features). Alternatively, can leverage `getCodebaseAST` and traverse ASTs.
*   **Example LLM Call:** `{"tool": "findSymbolDefinition", "parameters": {"symbolName": "Logger"}}`

### **3.3. Execution & Verification Tools**

#### **3.3.1. `runInTerminal`**
*   **Purpose:** Executes a shell command in a new or existing VS Code terminal.
*   **Input:** `command: string`, `description: string` (for logging/UI).
*   **Output:** `Promise<string>` (stdout/stderr combined) or `Promise<Error>`.
*   **VS Code API:** `vscode.window.createTerminal`, `terminal.sendText`, `terminal.show`.
*   **Advanced:** For capturing output reliably, consider `vscode.Pseudoterminal` or a custom Node.js `child_process` with output redirection. Initial implementation can focus on just running the command and showing the terminal.
*   **Example LLM Call:** `{"tool": "runInTerminal", "parameters": {"command": "npm install lodash", "description": "Installing Lodash library"}}`

#### **3.3.2. `generateTestForFile`**
*   **Purpose:** Generates a new unit test file for a given source file.
*   **Input:** `filePath: string`.
*   **Output:** `Promise<string>` (path to the generated test file) or `Promise<Error>`.
*   **Implementation:** Reads the source file, potentially uses `getCodebaseAST` to understand its exports/functions, and then prompts the LLM to generate test code. The generated code is then written to a new file (e.g., `filePath.test.ts`).
*   **Example LLM Call:** `{"tool": "generateTestForFile", "parameters": {"filePath": "src/utils/math.ts"}}`

#### **3.3.3. `runTests`**
*   **Purpose:** Executes the project's test suite.
*   **Input:** `testCommand: string` (e.g., `npm test`, `jest`, `yarn test`).
*   **Output:** `Promise<string>` (test output) or `Promise<Error>`.
*   **Implementation:** Internally calls `runInTerminal` with the provided `testCommand`.
*   **Example LLM Call:** `{"tool": "runTests", "parameters": {"testCommand": "npm test"}}`

### **3.4. User Interaction Tools**

#### **3.4.1. `showDiffForApproval`**
*   **Purpose:** Presents a diff view to the user for a proposed code change and waits for explicit approval or rejection.
*   **Input:** `filePath: string`, `newContent: string`.
*   **Output:** `Promise<'accepted' | 'rejected'>`.
*   **VS Code API:** `vscode.commands.executeCommand('vscode.diff', uri1, uri2, title)`. Requires creating temporary files for original and new content.
*   **UI Integration:** The webview UI must display "Accept" and "Reject" buttons that send messages back to the extension to resolve this promise.
*   **Example LLM Call:** `{"tool": "showDiffForApproval", "parameters": {"filePath": "src/component.tsx", "newContent": "<div>New Component</div>"}}`

#### **3.4.2. `askUserForInput`**
*   **Purpose:** Pauses agent execution and prompts the user for a specific piece of information.
*   **Input:** `question: string`.
*   **Output:** `Promise<string>` (user's response).
*   **VS Code API:** `vscode.window.showInputBox` or a custom input field in the webview.
*   **UI Integration:** The webview UI must display the question and an input field, sending the user's response back to the extension.
*   **Example LLM Call:** `{"tool": "askUserForInput", "parameters": {"question": "What is the name of the new feature you want to add?"}}`

### **3.5. Knowledge Tools**

#### **3.5.1. `google_web_search`**
*   **Purpose:** Performs a web search to gather external information.
*   **Input:** `query: string`.
*   **Output:** `Promise<string>` (summarized search results).
*   **Implementation:** Uses the `default_api.google_web_search` tool.
*   **Example LLM Call:** `{"tool": "google_web_search", "parameters": {"query": "best practices for Node.js error handling"}}`

---

## **Part 4: Technical Architecture & Core Components**

### **4.1. Monorepo Structure**
```
/gemini-assistant
|-- .vscode/                # VS Code launch and task configurations
|-- .github/                # GitHub Actions for CI/CD
|-- dist/                   # Bundled output files (extension.js, webview.bundle.js)
|-- node_modules/
|-- src/                    # Extension Host (backend) source code
|   |-- agent/              # AgentExecutor, Plan management, Agent state machine
|   |   |-- AgentExecutor.ts
|   |   |-- AgentState.ts   # Enum for agent states (IDLE, PLANNING, EXECUTING, PAUSED, etc.)
|   |-- controllers/        # ToolController, responsible for executing tools
|   |   |-- ToolController.ts
|   |-- providers/          # WebviewPanelProvider, manages webview lifecycle and communication
|   |   |-- WebviewPanelProvider.ts
|   |-- services/           # LLMService, handles all LLM interactions
|   |   |-- LLMService.ts
|   |-- managers/           # ApiKeyManager, handles secure API key storage
|   |   |-- ApiKeyManager.ts
|   |-- types/              # Shared TypeScript types for messages, plans, tools
|   |   |-- messaging.ts
|   |   |-- agent.ts
|   |   |-- tools.ts
|   |-- extension.ts        # Main extension activation entry point
|-- webview-ui/             # React UI (frontend) source code
|   |-- public/             # Static assets for webview
|   |-- src/
|   |   |-- components/     # Reusable React UI components (ChatBubble, PlanStep, DiffViewer)
|   |   |-- hooks/          # Custom React hooks (useVscodeApi, useChatHistory)
|   |   |   |-- useVscodeApi.ts
|   |   |-- modes/          # Components for each mode (ChatMode, AgentMode, AutoAgentMode)
|   |   |-- App.tsx         # Main React application component
|   |   |-- index.tsx       # React entry point
|   |-- package.json
|   |-- tsconfig.json
|-- .gitignore
|-- package.json            # Root package.json for extension
|-- tsconfig.json           # Root tsconfig for extension
|-- webpack.config.js       # Webpack configuration for both extension and webview
```

### **4.2. IPC Communication (Extension <-> Webview)**
*   **Mechanism:** `vscode.Webview.postMessage()` and `webview.onDidReceiveMessage()`.
*   **Type Safety:** All messages will adhere to interfaces defined in `src/types/messaging.ts`.
    ```typescript
    // src/types/messaging.ts
    export type WebviewToExtensionMessage =
      | { type: 'userQuery'; payload: string; mode: 'chat' | 'agent' | 'auto-agent' }
      | { type: 'approvePlan'; payload: { planId: string } }
      | { type: 'acceptDiff'; payload: { diffId: string } }
      | { type: 'rejectDiff'; payload: { diffId: string } }
      | { type: 'userInput'; payload: { questionId: string; response: string } }
      | { type: 'stopAgent' };

    export type ExtensionToWebviewMessage =
      | { type: 'aiResponse'; payload: string; mode: 'chat' }
      | { type: 'planGenerated'; payload: Plan; mode: 'agent' }
      | { type: 'planStepUpdate'; payload: PlanStep; mode: 'agent' }
      | { type: 'showDiff'; payload: { diffId: string; original: string; modified: string; filePath: string }; mode: 'agent' }
      | { type: 'askUser'; payload: { questionId: string; question: string }; mode: 'agent' | 'auto-agent' }
      | { type: 'agentThought'; payload: string; mode: 'auto-agent' }
      | { type: 'agentAction'; payload: { tool: string; params: any }; mode: 'auto-agent' }
      | { type: 'agentResult'; payload: string; mode: 'auto-agent' }
      | { type: 'agentError'; payload: string; mode: 'auto-agent' }
      | { type: 'agentStatus'; payload: AgentState; mode: 'all' };
    ```

### **4.3. `LLMService`**
*   **Purpose:** Encapsulates all interactions with the Gemini API.
*   **Key Methods:**
    *   `constructor(apiKey: string)`
    *   `getGenerativeModel(modelName: string)`: Returns a configured model instance.
    *   `generateContent(prompt: string | Array<Part | string>, tools?: Tool[]): Promise<GenerateContentResult>`: Handles text generation and function calling.
    *   `streamContent(prompt: string | Array<Part | string>, tools?: Tool[]): AsyncGenerator<GenerateContentResult>`: For streaming responses (especially in `Auto-Agent` mode).
*   **Function Calling Integration:**
    *   The `LLMService` will dynamically provide the `ToolController`'s available tools to the Gemini model.
    *   It will parse `FunctionCall` responses and delegate execution to the `ToolController`.

### **4.4. `ApiKeyManager`**
*   **Purpose:** Securely stores and retrieves the Gemini API key.
*   **Key Methods:**
    *   `getApiKey(): Promise<string | undefined>`
    *   `setApiKey(key: string): Promise<void>`
    *   `promptForApiKey(): Promise<string>`: Uses `vscode.window.showInputBox`.

### **4.5. `ToolController`**
*   **Purpose:** A central registry and executor for all agent tools.
*   **Key Methods:**
    *   `constructor(context: vscode.ExtensionContext)`
    *   `registerTool(toolName: string, toolFunction: Function, toolDefinition: Tool): void`: Registers a tool and its Gemini API definition.
    *   `executeTool(toolName: string, args: any[]): Promise<any>`: Executes a registered tool.
    *   `getToolDefinitions(): Tool[]`: Returns all registered tool definitions for LLM function calling.
*   **Tool Implementations:** Each tool described in Part 3 will be a private method within `ToolController` or a separate helper function called by `ToolController`.

### **4.6. `AgentExecutor`**
*   **Purpose:** The orchestrator of the agent's intelligence. Manages planning, execution, state, and interaction with the UI.
*   **Key Methods:**
    *   `constructor(llmService: LLMService, toolController: ToolController, webviewProvider: WebviewPanelProvider)`
    *   `startTask(userQuery: string, mode: 'chat' | 'agent' | 'auto-agent'): Promise<void>`
    *   `handleWebviewMessage(message: WebviewToExtensionMessage): Promise<void>`: Processes messages from the UI (e.g., `approvePlan`, `acceptDiff`).
    *   `_generatePlan(prompt: string): Promise<Plan>`
    *   `_executePlan(plan: Plan): Promise<void>`
    *   `_executeStep(step: PlanStep): Promise<void>`
    *   `_updateState(newState: AgentState): void`: Notifies UI of state changes.
*   **State Machine:** Manages the agent's current operational state (e.g., `IDLE`, `PLANNING`, `AWAITING_PLAN_APPROVAL`, `EXECUTING_STEP`, `AWAITING_DIFF_APPROVAL`, `COMPLETED`, `ERROR`).

---

## **Part 5: Phase-by-Phase Implementation Plan & Granular Tasks**

This section provides an exhaustive, task-level breakdown for building the Gemini Assistant. Each task includes specific implementation details, file paths, and considerations.

### **Phase 1: Foundation & `Chat` Mode**

*   **Goal:** Establish the core project, communication, and a functional read-only `Chat` mode.
*   **Estimated Time:** 3-4 days

#### **Tasks:**

- [ ] **1.1. Project Scaffolding & Monorepo Setup**
    - [ ] Run `yo code` (`TypeScript`, `Webpack`, `gemini-assistant`).
    - [ ] Create `webview-ui/` directory.
    - [ ] `cd webview-ui && npm init -y && npm install react react-dom @types/react @types/react-dom typescript @vscode/webview-ui-toolkit`.
    - [ ] Configure `webpack.config.js` to bundle `webview-ui/src/index.tsx` into `dist/webview.bundle.js`.
    - [ ] Configure `tsconfig.json` in `webview-ui/` for React JSX.
    - [ ] Initial Git commit: "feat: Initial project scaffold and monorepo setup."

- [ ] **1.2. `WebviewPanelProvider` Implementation**
    - [ ] Create `src/providers/WebviewPanelProvider.ts`.
    - [ ] Implement `createOrShow(extensionUri: vscode.Uri)` static method.
    - [ ] Implement `_getHtmlForWebview(webview: vscode.Webview, extensionUri: vscode.Uri)` to load `webview.bundle.js` with CSP and nonce.
    - [ ] Register command `geminiAssistant.start` in `extension.ts` to call `WebviewPanelProvider.createOrShow()`.
    - [ ] Test: Run extension, execute command, verify empty webview opens.

- [ ] **1.3. React UI Shell & IPC Bridge**
    - [ ] Create `webview-ui/src/index.tsx` to render `App.tsx`.
    - [ ] Create `webview-ui/src/App.tsx` with basic layout (header, main content, input).
    - [ ] Create `webview-ui/src/hooks/useVscodeApi.ts` for `acquireVsCodeApi` and `postMessage`.
    - [ ] Implement `webview.onDidReceiveMessage` in `WebviewPanelProvider` to log incoming messages.
    - [ ] Implement `postMessage` from `App.tsx` (e.g., a "Hello" button).
    - [ ] Test: Verify messages sent from webview are logged in extension host.

- [ ] **1.4. `LLMService` & `ApiKeyManager`**
    - [ ] Create `src/services/LLMService.ts`.
    - [ ] `npm install @google/generative-ai` in root.
    - [ ] Implement `generateContent` method.
    - [ ] Create `src/managers/ApiKeyManager.ts`.
    - [ ] Implement `getApiKey`, `setApiKey`, `promptForApiKey` using `vscode.SecretStorage`.
    - [ ] Integrate `ApiKeyManager` into `extension.ts` activation.
    - [ ] Test: Verify API key prompt on first run and successful storage.

- [ ] **1.5. `ToolController` & Read-Only Tools**
    - [ ] Create `src/controllers/ToolController.ts`.
    - [ ] Implement `readFile(path: string)`: Use `vscode.workspace.fs.readFile`. Handle `FileNotFound` error.
    - [ ] Implement `readDirectory(path: string)`: Use `vscode.workspace.fs.readDirectory`.
    - [ ] Implement `getCodebaseAST(globPattern: string)`:
        - [ ] `npm install typescript` in root.
        - [ ] Use `ts.createSourceFile` and `ts.forEachChild` to parse and serialize AST.
    - [ ] Implement `findSymbolDefinition(symbolName: string)`: Use `vscode.commands.executeCommand('vscode.executeDefinitionProvider', ...)` or AST traversal.
    - [ ] Implement `google_web_search(query: string)`: Use `default_api.google_web_search` (requires agent to have access to this tool).
    - [ ] Register these tools in `ToolController` with their Gemini function definitions.

- [ ] **1.6. `Chat` Mode Logic & UI Integration**
    - [ ] Create `webview-ui/src/modes/ChatMode.tsx`.
    - [ ] In `App.tsx`, use a state variable to switch between modes.
    - [ ] In `WebviewPanelProvider`, when `userQuery` message is received with `mode: 'chat'`:
        - [ ] Call `LLMService.generateContent` with the user's query and read-only tool definitions.
        - [ ] If LLM suggests a tool call, execute it via `ToolController`.
        - [ ] Send the tool result back to LLM for final response.
        - [ ] Send `aiResponse` message to webview.
    - [ ] Implement AI response rendering in `ChatMode.tsx` (markdown, code highlighting, copy button).
    - [ ] Test: Verify user can ask questions, agent uses tools, and responses are rendered correctly.

### **Phase 2: `Agent` Mode & Supervised Writing**

*   **Goal:** Enable the agent to propose and execute multi-step plans, with user approval for code modifications.
*   **Estimated Time:** 4-5 days

#### **Tasks:**

- [ ] **2.1. `AgentExecutor` Core**
    - [ ] Create `src/agent/AgentExecutor.ts`.
    - [ ] Define `AgentState` enum (`IDLE`, `PLANNING`, `AWAITING_PLAN_APPROVAL`, `EXECUTING_STEP`, `AWAITING_DIFF_APPROVAL`, `COMPLETED`, `ERROR`).
    - [ ] Implement `startTask(userQuery: string, mode: 'agent')`.
    - [ ] Implement `_generatePlan(prompt: string)`: LLM call to generate a `Plan` object.
    - [ ] Implement `_executePlan(plan: Plan)`: Loop through steps.
    - [ ] Implement `_executeStep(step: PlanStep)`: Execute tool call via `ToolController`.
    - [ ] Implement `_updateState(newState: AgentState)`: Send `agentStatus` message to webview.

- [ ] **2.2. `Agent` Mode UI & Plan View**
    - [ ] Create `webview-ui/src/modes/AgentMode.tsx`.
    - [ ] Design the two-panel layout (Plan View, Conversation/Context).
    - [ ] Implement React components for `PlanView` and `PlanStep` (displaying status, description).
    - [ ] Add "Generate Plan", "Approve & Run Plan", "Pause/Resume", "Cancel Task" buttons.
    - [ ] Update `App.tsx` to switch to `AgentMode` when selected.

- [ ] **2.3. Implement Write Tools**
    - [ ] `ToolController`: Implement `writeFile(path: string, content: string)`.
    - [ ] `ToolController`: Implement `createFile(path: string)`.
    - [ ] `ToolController`: Implement `moveFile(source: string, destination: string)`.
    - [ ] Register these tools with their Gemini function definitions.

- [ ] **2.4. `showDiffForApproval` Tool & UI Integration**
    - [ ] `ToolController`: Implement `showDiffForApproval(filePath: string, newContent: string)`.
        - [ ] Create temporary files for original and new content.
        - [ ] Use `vscode.commands.executeCommand('vscode.diff', ...)` to open diff editor.
        - [ ] Send `showDiff` message to webview with `diffId`, `original`, `modified`, `filePath`.
        - [ ] Pause execution until `acceptDiff` or `rejectDiff` message is received from webview.
    - [ ] In `AgentMode.tsx`, render a diff viewer component when `showDiff` message is received.
    - [ ] Add "Accept Change" / "Reject Change" buttons to the diff viewer.
    - [ ] Implement `acceptDiff` and `rejectDiff` messages from webview to extension.

- [ ] **2.5. Agent Mode Workflow Integration**
    - [ ] In `AgentExecutor`, after `_generatePlan`, send `planGenerated` message to webview and set state to `AWAITING_PLAN_APPROVAL`.
    - [ ] On `approvePlan` message, set state to `EXECUTING_STEP` and start `_executePlan`.
    - [ ] Modify `_executeStep` for `writeFile` (and other write tools) to call `showDiffForApproval` and await its result.
    - [ ] Test: Provide a task involving file modification. Verify plan generation, diff approval, and successful file write.

### **Phase 3: Terminal Integration & Self-Verification**

*   **Goal:** The agent can execute shell commands and verify its work by running tests.
*   **Estimated Time:** 3-4 days

#### **Tasks:**

- [ ] **3.1. `runInTerminal` Tool Implementation**
    - [ ] `ToolController`: Implement `runInTerminal(command: string, description: string)`.
    - [ ] Use `vscode.window.createTerminal` and `terminal.sendText`.
    - [ ] **Advanced:** Implement output capture.
        - [ ] Option A (Simpler): Use `child_process.exec` or `spawn` in Node.js, capture stdout/stderr, and send to webview.
        - [ ] Option B (Complex, but native): Use `vscode.Pseudoterminal` for full control over terminal I/O.
    - [ ] Register tool with Gemini definition.
    - [ ] Test: Agent can run `npm install` or `ls -la` and report output.

- [ ] **3.2. `generateTestForFile` Tool**
    - [ ] `ToolController`: Implement `generateTestForFile(filePath: string)`.
    - [ ] Logic: Read `filePath`, use `getCodebaseAST` to get context, prompt LLM to generate test code, then use `writeFile` to create `filePath.test.ts`.
    - [ ] Register tool with Gemini definition.
    - [ ] Test: Agent can generate a basic test file for a given source file.

- [ ] **3.3. `runTests` Tool**
    - [ ] `ToolController`: Implement `runTests(testCommand: string)`.
    - [ ] Logic: Internally call `runInTerminal` with the provided `testCommand`.
    - [ ] Register tool with Gemini definition.
    - [ ] Test: Agent can run `npm test` and report the output.

- [ ] **3.4. Agent Planning Enhancement for Verification**
    - [ ] Update the system prompt for the `AgentExecutor`'s `_generatePlan` method to encourage including `runTests` after code modifications.
    - [ ] Test: Agent's generated plans now include a test verification step.

### **Phase 4: `Auto-Agent` Mode & Full Autonomy**

*   **Goal:** The agent can work autonomously on high-level goals, providing real-time thought streams.
*   **Estimated Time:** 3-4 days

#### **Tasks:**

- [ ] **4.1. `Auto-Agent` Mode UI**
    - [ ] Create `webview-ui/src/modes/AutoAgentMode.tsx`.
    - [ ] Design the UI for a continuous, scrolling thought/action log.
    - [ ] Implement components to display `[THOUGHT]`, `[ACTION]`, `[RESULT]`, `[ERROR]` entries with timestamps and distinct styling.
    - [ ] Add a prominent "Stop Execution" button.

- [ ] **4.2. `AgentExecutor` for Autonomy**
    - [ ] Modify `AgentExecutor.startTask` to handle `mode: 'auto-agent'`.
    - [ ] Implement a continuous loop where the agent:
        1.  Generates a thought (LLM call).
        2.  Generates an action (LLM call, potentially tool call).
        3.  Executes action via `ToolController`.
        4.  Generates a result/observation (from tool output).
        5.  Feeds result back to LLM for next thought/action.
    - [ ] This loop should not pause for user approval (except for `askUserForInput`).
    - [ ] Implement `stopAgent` message handling to gracefully terminate the loop.

- [ ] **4.3. `askUserForInput` Tool**
    - [ ] `ToolController`: Implement `askUserForInput(question: string)`.
    - [ ] Send `askUser` message to webview with `questionId` and `question`.
    - [ ] Pause `AgentExecutor` until `userInput` message is received from webview.
    - [ ] In `AutoAgentMode.tsx`, display the question and an input field.

- [ ] **4.4. LLM Streaming Integration**
    - [ ] In `LLMService`, use `streamContent` for `Auto-Agent` mode to provide real-time thought updates to the UI.
    - [ ] In `AutoAgentMode.tsx`, update the thought stream as chunks are received.

- [ ] **4.5. Error Recovery & Self-Correction**
    - [ ] Enhance `AgentExecutor` to handle tool errors gracefully.
    - [ ] If a tool fails, feed the error back to the LLM and prompt it to self-correct or ask for user input.

### **Phase 5: Polishing, Testing & Deployment**

*   **Goal:** Ensure the extension is robust, user-friendly, and ready for public release.
*   **Estimated Time:** 3-4 days

#### **Tasks:**

- [ ] **5.1. Comprehensive Testing**
    - [ ] **Unit Tests:**
        - [ ] `npm install --save-dev mocha @types/mocha @types/vscode` in root.
        - [ ] Write tests for `LLMService`, `ApiKeyManager`, `ToolController` (mocking VS Code APIs).
    - [ ] **Integration Tests:**
        - [ ] Use `@vscode/test-electron` for end-to-end tests.
        - [ ] Test activation, webview opening, message passing, and basic agent workflows in each mode.
    - [ ] **Manual Testing:** Thoroughly test all features across different projects and scenarios.

- [ ] **5.2. Documentation & User Experience**
    - [ ] Create a high-quality `README.md` in the root.
        - [ ] Clear description, installation instructions, usage guide for each mode.
        - [ ] Animated GIFs demonstrating key features.
        - [ ] Troubleshooting section.
    - [ ] Add tooltips and clear labels to all UI elements.
    - [ ] Implement loading indicators for long-running operations.

- [ ] **5.3. CI/CD Pipeline**
    - [ ] Create `.github/workflows/main.yml`.
    - [ ] Configure GitHub Actions to:
        - [ ] Run `npm install` and `npm test` on push/PR.
        - [ ] Lint code (`eslint`).
        - [ ] (Optional) Automatically package `.vsix` on release tag.

- [ ] **5.4. Packaging & Publishing**
    - [ ] Ensure `package.json` (root) has all required fields: `publisher`, `displayName`, `description`, `categories`, `icon`, `repository`, `bugs`, `homepage`.
    - [ ] Run `vsce package` to create the `.vsix` file.
    - [ ] Create a publisher account on the VS Code Marketplace.
    - [ ] Run `vsce publish` to release the extension.

---

## **Part 6: Agent's Core Directive (System Prompt)**

This prompt will be the foundation of the LLM's understanding and behavior. It will be dynamically updated with tool definitions and context.

```
You are Gemini, an advanced AI software engineering assistant operating within a VS Code extension. Your primary goal is to assist the user in coding tasks, from simple explanations to complex refactoring and feature implementation.

You operate in distinct modes, each with specific rules and interaction patterns:

---
**MODE: CHAT**
**Purpose:** Answer questions, explain code, provide information. You are read-only.
**Rules:**
- You cannot modify any files or execute commands that change the system state.
- Use your available read-only tools to gather information.
- Provide concise, accurate, and well-formatted responses.
- If asked to perform a write action, politely decline and suggest switching to 'Agent' mode.

---
**MODE: AGENT**
**Purpose:** Execute specific, well-defined coding tasks that may involve modifying the codebase. You are supervised and require explicit user approval for significant changes.
**Rules:**
- **PLAN FIRST:** Always generate a detailed, step-by-step plan before taking any action. Present this plan to the user for approval.
- **EXECUTE SEQUENTIALLY:** Execute steps one by one.
- **APPROVE CHANGES:** Before any `writeFile`, `createFile`, or `moveFile` operation, you MUST use the `showDiffForApproval` tool. Pause execution and wait for user acceptance. If rejected, report to the user and re-plan if necessary.
- **VERIFY:** After making changes, consider using `runTests` to verify your work.
- **REPORT:** Clearly report the outcome of each step.

---
**MODE: AUTO-AGENT**
**Purpose:** Autonomously achieve high-level coding goals. You operate with minimal supervision, logging your thought process and actions in real-time.
**Rules:**
- **AUTONOMOUS EXECUTION:** Do not wait for explicit approval for each step or file modification.
- **THINK ALOUD:** Continuously stream your internal thoughts, reasoning, and actions to the user. Use `[THOUGHT]`, `[ACTION]`, `[RESULT]`, `[ERROR]` prefixes.
- **SELF-CORRECT:** If a tool fails or an action doesn't yield the expected result, analyze the error and attempt to self-correct.
- **ASK IF STUCK:** If you are truly stuck and cannot proceed autonomously, use the `askUserForInput` tool to request clarification.
- **REPORT COMPLETION:** Provide a comprehensive summary upon successful completion or unrecoverable failure.

---
**AVAILABLE TOOLS:**
[Dynamically injected tool definitions from ToolController.getToolDefinitions()]

**CURRENT CONTEXT:**
[Dynamically injected context, e.g., current file open, selected text, previous conversation history, relevant codebase snippets.]

**USER REQUEST:**
[The user's current prompt or task.]

**YOUR RESPONSE FORMAT:**
Your response should be a JSON object.
If you are in 'CHAT' mode and providing a final answer, use:
```json
{
  "mode": "chat",
  "response": "Your natural language answer here, with markdown and code blocks."
}
```
If you are in 'AGENT' mode and generating a plan, use:
```json
{
  "mode": "agent",
  "plan": [
    {"id": 1, "description": "Step 1 description", "toolCall": {"tool": "toolName", "parameters": {}}},
    {"id": 2, "description": "Step 2 description", "toolCall": {"tool": "toolName", "parameters": {}}}
  ]
}
```
If you are in 'AGENT' or 'AUTO-AGENT' mode and executing a step, or in 'AUTO-AGENT' mode and thinking aloud, use:
```json
{
  "mode": "agent" | "auto-agent",
  "thought": "Your internal thought process.",
  "action": {"tool": "toolName", "parameters": {}}, // Optional: if you are performing an action
  "result": "Result of the previous action." // Optional: if you are reporting a result
}
```
If you need to ask the user for input:
```json
{
  "mode": "agent" | "auto-agent",
  "action": {"tool": "askUserForInput", "parameters": {"question": "Your question here."}}
}
```
If you need to show a diff for approval (only in 'AGENT' mode):
```json
{
  "mode": "agent",
  "action": {"tool": "showDiffForApproval", "parameters": {"filePath": "path/to/file.ts", "newContent": "new file content"}}
}
```
Always ensure your JSON is valid and complete. Do not include any other text outside the JSON block.
``` @



